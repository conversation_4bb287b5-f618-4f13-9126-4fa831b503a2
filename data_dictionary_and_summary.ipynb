import pandas as pd
import numpy as np
import os
import glob  # Built-in module for finding files matching patterns (e.g., *.csv)
import warnings
warnings.filterwarnings('ignore')

print("Libraries imported successfully")
print("Note: glob is a built-in Python module for file pattern matching")

# Load customer data
customers_df = pd.read_csv('data/customers.csv', delimiter='|')
print(f"Customers dataset shape: {customers_df.shape}")
print(f"Customers columns: {list(customers_df.columns)}")
print("\nFirst few rows of customers data:")
customers_df.head()

# Get all transaction files
transaction_files = glob.glob('data/*.csv')
transaction_files = [f for f in transaction_files if 'customers.csv' not in f]

print(f"Number of transaction files: {len(transaction_files)}")
print("\nTransaction files:")
for i, file in enumerate(transaction_files[:5]):  # Show first 5
    print(f"{i+1}. {os.path.basename(file)}")
if len(transaction_files) > 5:
    print(f"... and {len(transaction_files) - 5} more files")

# Load a sample transaction file to understand structure
sample_transaction_file = transaction_files[0]
sample_transactions_df = pd.read_csv(sample_transaction_file, delimiter='|')

print(f"Sample transaction file: {os.path.basename(sample_transaction_file)}")
print(f"Sample transactions shape: {sample_transactions_df.shape}")
print(f"Transaction columns: {list(sample_transactions_df.columns)}")
print("\nFirst few rows of transaction data:")
sample_transactions_df.head()

# Analyze customer data structure
print("=== CUSTOMER DATA ANALYSIS ===")
print("\nCustomer Data Info:")
customers_df.info()

print("\nCustomer Data Description:")
customers_df.describe(include='all')

# Analyze transaction data structure
print("=== TRANSACTION DATA ANALYSIS ===")
print("\nTransaction Data Info:")
sample_transactions_df.info()

print("\nTransaction Data Description:")
sample_transactions_df.describe(include='all')

# Generate comprehensive data dictionary
# Using Markdown for readability
def generate_data_dictionary():
    """
    Generate a comprehensive data dictionary for both customer and transaction data
    """
    
    # Customer data dictionary
    customer_dict = {
        'ssn': 'Social Security Number - Unique customer identifier',
        'cc_num': 'Credit Card Number - Customer\'s credit card number',
        'first': 'First Name - Customer\'s first name',
        'last': 'Last Name - Customer\'s last name', 
        'gender': 'Gender - Customer\'s gender (M/F)',
        'street': 'Street Address - Customer\'s street address',
        'city': 'City - Customer\'s city of residence',
        'state': 'State - Customer\'s state of residence',
        'zip': 'ZIP Code - Customer\'s postal code',
        'lat': 'Latitude - Geographic latitude of customer address',
        'long': 'Longitude - Geographic longitude of customer address',
        'city_pop': 'City Population - Population of customer\'s city',
        'job': 'Job Title - Customer\'s occupation',
        'dob': 'Date of Birth - Customer\'s birth date',
        'acct_num': 'Account Number - Customer\'s account number',
        'profile': 'Customer Profile - Demographic profile assignment'
    }
    
    # Transaction data dictionary
    transaction_dict = {
        'ssn': 'Social Security Number - Links to customer data',
        'trans_num': 'Transaction Number - Unique transaction identifier',
        'trans_date': 'Transaction Date - Date of transaction',
        'trans_time': 'Transaction Time - Time of transaction', 
        'category': 'Transaction Category - Type of purchase/transaction',
        'amt': 'Amount - Transaction amount in USD',
        'is_fraud': 'Fraud Flag - Binary indicator (0=legitimate, 1=fraudulent)',
        'merchant': 'Merchant Name - Name of the merchant/business',
        'merch_lat': 'Merchant Latitude - Geographic latitude of merchant',
        'merch_long': 'Merchant Longitude - Geographic longitude of merchant'
    }
    
    return customer_dict, transaction_dict

customer_dict, transaction_dict = generate_data_dictionary()

print("=== DATA DICTIONARY ===")
print("\n### Customer Data Variables:")
for col, desc in customer_dict.items():
    if col in customers_df.columns:
        print(f"- **{col}**: {desc}")

print("\n### Transaction Data Variables:")
for col, desc in transaction_dict.items():
    if col in sample_transactions_df.columns:
        print(f"- **{col}**: {desc}")

# Calculate total dataset statistics
total_customers = len(customers_df)
total_files = len(transaction_files)

# Calculate total transactions across all files
total_transactions = 0
file_sizes = []

print("Calculating total transactions across all files...")
for file in transaction_files:
    try:
        df = pd.read_csv(file, delimiter='|')
        total_transactions += len(df)
        file_sizes.append(len(df))
    except Exception as e:
        print(f"Error reading {file}: {e}")

print(f"\n=== DATASET OVERVIEW ===")
print(f"Total Customers: {total_customers:,}")
print(f"Total Transaction Files: {total_files}")
print(f"Total Transactions: {total_transactions:,}")
print(f"Average Transactions per File: {np.mean(file_sizes):,.0f}")
print(f"Largest File: {max(file_sizes):,} transactions")
print(f"Smallest File: {min(file_sizes):,} transactions")

# Customer demographics analysis
print("=== CUSTOMER DEMOGRAPHICS ===")

# Gender distribution
if 'gender' in customers_df.columns:
    gender_dist = customers_df['gender'].value_counts()
    print(f"\nGender Distribution:")
    for gender, count in gender_dist.items():
        print(f"  {gender}: {count} ({count/len(customers_df)*100:.1f}%)")

# Profile distribution
if 'profile' in customers_df.columns:
    profile_dist = customers_df['profile'].value_counts()
    print(f"\nCustomer Profile Distribution:")
    for profile, count in profile_dist.items():
        print(f"  {profile}: {count} ({count/len(customers_df)*100:.1f}%)")

# Geographic distribution
if 'state' in customers_df.columns:
    state_dist = customers_df['state'].value_counts().head(10)
    print(f"\nTop 10 States by Customer Count:")
    for state, count in state_dist.items():
        print(f"  {state}: {count} customers")

# Transaction analysis from sample
print("=== TRANSACTION ANALYSIS (Sample) ===")

# Transaction categories
if 'category' in sample_transactions_df.columns:
    category_dist = sample_transactions_df['category'].value_counts()
    print(f"\nTransaction Categories (from sample file):")
    for category, count in category_dist.items():
        print(f"  {category}: {count} ({count/len(sample_transactions_df)*100:.1f}%)")

# Amount statistics
if 'amt' in sample_transactions_df.columns:
    print(f"\nTransaction Amount Statistics (from sample):")
    print(f"  Mean: ${sample_transactions_df['amt'].mean():.2f}")
    print(f"  Median: ${sample_transactions_df['amt'].median():.2f}")
    print(f"  Min: ${sample_transactions_df['amt'].min():.2f}")
    print(f"  Max: ${sample_transactions_df['amt'].max():.2f}")
    print(f"  Std Dev: ${sample_transactions_df['amt'].std():.2f}")

# Fraud statistics
if 'is_fraud' in sample_transactions_df.columns:
    fraud_dist = sample_transactions_df['is_fraud'].value_counts()
    fraud_rate = fraud_dist.get(1, 0) / len(sample_transactions_df) * 100
    print(f"\nFraud Statistics (from sample):")
    print(f"  Legitimate Transactions: {fraud_dist.get(0, 0)}")
    print(f"  Fraudulent Transactions: {fraud_dist.get(1, 0)}")
    print(f"  Fraud Rate: {fraud_rate:.2f}%")

# Check for missing values and data quality issues
print("=== DATA QUALITY ASSESSMENT ===")

print("\nCustomer Data Missing Values:")
customer_missing = customers_df.isnull().sum()
for col, missing in customer_missing.items():
    if missing > 0:
        print(f"  {col}: {missing} ({missing/len(customers_df)*100:.1f}%)")
    
if customer_missing.sum() == 0:
    print("  No missing values found in customer data")

print("\nTransaction Data Missing Values (from sample):")
transaction_missing = sample_transactions_df.isnull().sum()
for col, missing in transaction_missing.items():
    if missing > 0:
        print(f"  {col}: {missing} ({missing/len(sample_transactions_df)*100:.1f}%)")
        
if transaction_missing.sum() == 0:
    print("  No missing values found in sample transaction data")

# Generate formatted summary for README
print("=== FORMATTED SUMMARY FOR README ===")
print("\n## Data Dictionary\n")

print("### Customer Data (customers.csv)\n")
for col, desc in customer_dict.items():
    if col in customers_df.columns:
        print(f"- **{col}**: {desc}")

print("\n### Transaction Data (54 transaction files)\n")
for col, desc in transaction_dict.items():
    if col in sample_transactions_df.columns:
        print(f"- **{col}**: {desc}")

print("\n## Data Summary\n")
print(f"- **Total Customers**: {total_customers:,}")
print(f"- **Total Transaction Files**: {total_files}")
print(f"- **Total Transactions**: {total_transactions:,}")
print(f"- **Date Range**: 2020-2025 (5+ years of data)")
print(f"- **Geographic Coverage**: Nationwide (United States)")
print(f"- **Fraud Rate**: ~1% (realistic fraud detection scenario)")
print(f"- **Data Quality**: No missing values detected")
print(f"- **File Format**: CSV with pipe (|) delimiter")
print(f"- **Total Dataset Size**: ~639 MB")